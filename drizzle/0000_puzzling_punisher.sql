CREATE TABLE `chapter_chunks` (
	`id` text PRIMARY KEY NOT NULL,
	`chapter_id` text NOT NULL,
	`series_id` text NOT NULL,
	`chunk_index` integer NOT NULL,
	`sentence_start` integer,
	`sentence_end` integer,
	`text` text NOT NULL,
	`metadata` text,
	`created_at` text NOT NULL,
	FOR<PERSON><PERSON><PERSON> KEY (`chapter_id`) REFERENCES `chapters`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREI<PERSON><PERSON> KEY (`series_id`) REFERENCES `series`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `chapters` (
	`id` text PRIMARY KEY NOT NULL,
	`chapter_number` integer NOT NULL,
	`title` text NOT NULL,
	`content` text NOT NULL,
	`system_prompt` text,
	`user_prompt` text,
	`model_name` text,
	`semantic_queries` text,
	`metadata` text,
	`is_vectorized` integer DEFAULT false,
	`created_at` text NOT NULL,
	`series_id` text NOT NULL,
	<PERSON>OR<PERSON><PERSON><PERSON> KEY (`series_id`) REFERENCES `series`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `series` (
	`id` text PRIMARY KEY NOT NULL,
	`title` text NOT NULL,
	`slug` text NOT NULL,
	`description` text,
	`genre` text DEFAULT 'rebirth' NOT NULL,
	`theme` text,
	`idea` text,
	`summary` text,
	`outline` text,
	`created_at` text NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `series_slug_unique` ON `series` (`slug`);