{"version": "6", "dialect": "sqlite", "id": "978e3c30-4441-42a3-a8cf-1b8c5f6a274d", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"chapter_chunks": {"name": "chapter_chunks", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "chapter_id": {"name": "chapter_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "series_id": {"name": "series_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "chunk_index": {"name": "chunk_index", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "sentence_start": {"name": "sentence_start", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "sentence_end": {"name": "sentence_end", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"chapter_chunks_chapter_id_chapters_id_fk": {"name": "chapter_chunks_chapter_id_chapters_id_fk", "tableFrom": "chapter_chunks", "tableTo": "chapters", "columnsFrom": ["chapter_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chapter_chunks_series_id_series_id_fk": {"name": "chapter_chunks_series_id_series_id_fk", "tableFrom": "chapter_chunks", "tableTo": "series", "columnsFrom": ["series_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "chapters": {"name": "chapters", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "chapter_number": {"name": "chapter_number", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "system_prompt": {"name": "system_prompt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_prompt": {"name": "user_prompt", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "model_name": {"name": "model_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "semantic_queries": {"name": "semantic_queries", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_vectorized": {"name": "is_vectorized", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "series_id": {"name": "series_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"chapters_series_id_series_id_fk": {"name": "chapters_series_id_series_id_fk", "tableFrom": "chapters", "tableTo": "series", "columnsFrom": ["series_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "series": {"name": "series", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "genre": {"name": "genre", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'rebirth'"}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "idea": {"name": "idea", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "summary": {"name": "summary", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "outline": {"name": "outline", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"series_slug_unique": {"name": "series_slug_unique", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}