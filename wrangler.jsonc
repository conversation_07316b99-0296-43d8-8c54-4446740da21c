/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "truyenhay",
	"compatibility_flags": ["nodejs_compat"],
	"compatibility_date": "2025-06-17",
	"pages_build_output_dir": ".svelte-kit/cloudflare",
	"observability": {
		"enabled": true
	},
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	"placement": { "mode": "off" },

	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "truyenmoigiay-db",
			"database_id": "b5d57f0b-bc97-40f8-a15b-a9d1ad66c085",
			"migrations_dir": "./drizzle"
		}
	],

	/**
	 * AI Binding for Workers AI
	 * https://developers.cloudflare.com/workers-ai/
	 */
	"vectorize": [
		{
			"binding": "VECTORIZE",
			"index_name": "truyenmoigiay-vectors"
		}
	]

	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */

	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */

	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },

	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
