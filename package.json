{"name": "truyenhay", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "npm run build && wrangler pages dev", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "deploy": "npm run build && wrangler pages deploy", "log": "wrangler pages deployment tail --project-name truyenhay", "cf-typegen": "wrangler types src/worker-configuration.d.ts", "db:generate": "drizzle-kit generate", "db:migrate:local": "wrangler d1 migrations apply DB --local", "db:migrate:prod": "wrangler d1 migrations apply DB --remote"}, "devDependencies": {"@eslint/compat": "^1.3.1", "@eslint/js": "^9.29.0", "@google/genai": "^1.7.0", "@sveltejs/adapter-cloudflare": "^7.0.4", "@sveltejs/kit": "^2.22.0", "@sveltejs/vite-plugin-svelte": "^6.0.0-next.1", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.10", "@types/better-sqlite3": "^7.6.13", "drizzle-kit": "^0.31.2", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-svelte": "^3.9.3", "globals": "^16.2.0", "js-tiktoken": "^1.0.20", "prettier": "^3.6.1", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.13", "svelte": "^5.34.8", "svelte-check": "^4.2.2", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "ulid": "^3.0.1", "vite": "^6.3.5", "wrangler": "^4.22.0", "zod": "^3.25.67"}, "dependencies": {"axios": "^1.10.0", "better-sqlite3": "^12.1.1", "drizzle-orm": "^0.44.2", "openai": "^5.7.0"}}