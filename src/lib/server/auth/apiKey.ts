// src/lib/server/auth/apiKey.ts

/**
 * Utility functions for API key management
 */

/**
 * Validates an API key format (basic validation)
 * @param apiKey The API key to validate
 * @returns true if the format is valid
 */
export function isValidApiKeyFormat(apiKey: string): boolean {
	// Basic validation: should be at least 32 characters long
	// and contain only alphanumeric characters and common symbols
	const apiKeyRegex = /^[A-Za-z0-9\-_\.]{32,}$/;
	return apiKeyRegex.test(apiKey);
}

/**
 * Generates a secure API key (for development/testing purposes)
 * In production, use a proper secret management system
 * @param length The length of the API key (default: 64)
 * @returns A randomly generated API key
 */
export function generateApiKey(length: number = 64): string {
	const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
	let result = '';
	for (let i = 0; i < length; i++) {
		result += chars.charAt(Math.floor(Math.random() * chars.length));
	}
	return result;
}

/**
 * Extracts API key from various sources in the request
 * @param request The incoming request
 * @returns The API key if found, null otherwise
 */
export function extractApiKey(request: Request): string | null {
	const url = new URL(request.url);
	
	// Check x-api-key header
	const headerApiKey = request.headers.get('x-api-key');
	if (headerApiKey) return headerApiKey;
	
	// Check Authorization header (Bearer token)
	const authHeader = request.headers.get('authorization');
	if (authHeader?.startsWith('Bearer ')) {
		return authHeader.replace('Bearer ', '');
	}
	
	// Check query parameter
	const queryApiKey = url.searchParams.get('api_key');
	if (queryApiKey) return queryApiKey;
	
	return null;
}

/**
 * Creates a standardized authentication error response
 * @param message The error message
 * @param status The HTTP status code
 * @returns Response object
 */
export function createAuthError(message: string, status: number = 401): Response {
	return new Response(
		JSON.stringify({
			error: 'Authentication Error',
			message,
			timestamp: new Date().toISOString()
		}),
		{
			status,
			headers: {
				'Content-Type': 'application/json',
				'WWW-Authenticate': 'Bearer realm="API"'
			}
		}
	);
}
