import { drizzle } from 'drizzle-orm/d1';
import type { D1Database } from '@cloudflare/workers-types';
import * as schema from './schema';
/**
 * Creates a Drizzle database client for Cloudflare D1.
 * @param d1 The D1 database binding from Cloudflare Workers environment.
 * @returns A Drizzle database client instance.
 */
export function createDbClient(d1: D1Database) {
	return drizzle(d1, { schema });
}

export type DbClient = ReturnType<typeof createDbClient>;
