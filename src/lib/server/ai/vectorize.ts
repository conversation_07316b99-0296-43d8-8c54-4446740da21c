// src/lib/server/ai/vectorizer.ts
import type { GenrePack } from '$lib/server/ai/genres/rebirth.genre';
import type { OpenAIClient } from '$lib/server/ai/openAI';
import type { Chapter } from '$lib/server/db/schema';

import { Tiktoken } from 'js-tiktoken/lite';
import o200k_base from 'js-tiktoken/ranks/o200k_base';

interface VectorizeParams {
	vectorizeIndex: VectorizeIndex;
	openAIClient: OpenAIClient;
	genrePack: GenrePack;
	chapter: Chapter;
}

function sanitizeChapterText(raw: string): string {
	return raw
		.replace(/###.*\n?/g, '') // remove markdown headers
		.replace(/\*\*(.*?)\*\*/g, '$1') // remove bold markers
		.replace(/[_*~`>]+/g, '') // remove leftover formatting
		.replace(/[\u200B-\u200D\uFEFF]/g, '') // remove zero-width chars
		.replace(/[^\S\r\n]{2,}/g, ' ') // multiple spaces → one space
		.replace(/\r\n|\r/g, '\n') // normalize newlines
		.replace(/\n{2,}/g, '\n') // collapse multiple newlines
		.trim(); // remove leading/trailing whitespace
}

async function chunkTextBySentence(fullText: string, maxTokens = 500, minTokens = 200, sentenceOverlap = 1) {
	const sentences = fullText
		.split(/(?<=[.!?。！？])\s+/)
		.map((s) => s.trim())
		.filter(Boolean);

	const chunks: {
		text: string;
		sentenceStart: number;
		sentenceEnd: number;
	}[] = [];

	const encoder = new Tiktoken(o200k_base);

	let i = 0;

	while (i < sentences.length) {
		let tokenCount = 0;
		const chunkSentences: string[] = [];
		const start = i;

		while (i < sentences.length) {
			const sentence = sentences[i];
			const tCount = encoder.encode(sentence).length;

			if (tokenCount + tCount > maxTokens && tokenCount >= minTokens) break;

			chunkSentences.push(sentence);
			tokenCount += tCount;
			i++;
		}

		const currentText = chunkSentences.join(' ');
		const lastText = chunks.length > 0 ? chunks[chunks.length - 1].text : '';

		// Drop duplicate chunks
		if (currentText !== lastText) {
			chunks.push({
				text: currentText,
				sentenceStart: start,
				sentenceEnd: i - 1
			});
		}

		// Go back a few sentences to overlap
		if (i < sentences.length) {
			i = Math.max(start + sentenceOverlap, i - sentenceOverlap);
		}
	}

	return chunks;
}

/**
 * INGESTION FLOW
 * Takes a chapter, chunks its content, creates embeddings via proxy, and upserts them into Vectorize.
 * @param vectorizeIndex - The Vectorize index to upsert into.
 * @param openAIClient - The OpenAI client instance.
 * @param genrePack - The configuration object for a specific genre.
 * @param chapter The full chapter object from the database.
 */
export async function ingestChapterIntoVectorize({
	openAIClient,
	vectorizeIndex,
	genrePack,
	chapter
}: VectorizeParams) {
	console.log(`[Vectorizer] Starting ingestion for Chapter ID: ${chapter.id}`);

	const cleanedContent = sanitizeChapterText(chapter.content);
	const chunks = await chunkTextBySentence(cleanedContent);

	if (chunks.length === 0) {
		console.log(`[Vectorizer] No content chunks to process for Chapter ID: ${chapter.id}.`);
		return [];
	}

	const parsedMetadata = typeof chapter.metadata === 'string' ? JSON.parse(chapter.metadata) : chapter.metadata || {};

	try {
		const inputStrings = chunks.map((chunk) => chunk.text);
		const embeddings = await openAIClient.embeddings({
			model: genrePack.vectorModel,
			input: inputStrings
		});

		if (!embeddings || embeddings.length !== chunks.length) {
			throw new Error('Embedding generation returned mismatched count.');
		}

		const vectors: VectorizeVector[] = chunks.map((chunk, i) => ({
			id: `ch${chapter.id}-chunk${i}`,
			values: embeddings[i],
			metadata: {
				seriesId: chapter.seriesId,
				chapterNumber: chapter.chapterNumber,
				chunkIndex: i,
				sentenceStart: chunk.sentenceStart,
				sentenceEnd: chunk.sentenceEnd,
				characters: parsedMetadata.characters || [],
				skills: parsedMetadata.skills || [],
				tags: parsedMetadata.tags || [],
				keywords: parsedMetadata.keywords || [],
				locations: parsedMetadata.locations || [],
				memoryType: parsedMetadata.memoryType || 'plot'
			}
		}));

		await vectorizeIndex.upsert(vectors);
		console.log(`[Vectorizer] Successfully upserted ${vectors.length} vectors for Chapter ID: ${chapter.id}.`);

		return vectors.map((vector, index) => ({
			...vector,
			metadata: {
				...vector.metadata,
				chunkIndex: index,
				sentenceStart: chunks[index].sentenceStart,
				sentenceEnd: chunks[index].sentenceEnd,
				text: inputStrings[index]
			}
		}));
	} catch (error) {
		console.error(`[Vectorizer] Failed to ingest Chapter ID: ${chapter.id}.`, error);
		throw new Error('Failed to ingest chapter.');
	}
}

interface RetrieveParams {
	vectorizeIndex: VectorizeIndex;
	openAIClient: OpenAIClient;
	genrePack: GenrePack;
	query: string;
	seriesId: number;
}

/**
 * RETRIEVAL FLOW
 * Takes a query, finds the most relevant context from the Vectorize index for a specific series.
 * @param vectorizeIndex - The Vectorize index to upsert into.
 * @param openAIClient - The OpenAI client instance.
 * @param query The search query.
 * @param seriesId The ID of the series to search within.
 * @returns A formatted string of the most relevant context snippets.
 */
export async function retrieveRelevantContext({
	openAIClient,
	vectorizeIndex,
	genrePack,
	query,
	seriesId
}: RetrieveParams): Promise<string> {
	console.log(`[Vectorizer] Retrieving context for query: "${query}"`);
	try {
		const [queryVector] = await openAIClient.embeddings({
			model: genrePack.vectorModel,
			input: query
		});

		if (!queryVector) throw new Error('Failed to generate query embedding.');

		const searchResults = await vectorizeIndex.query(queryVector, {
			topK: 20,
			returnMetadata: true,
			filter: { seriesId }
		});

		console.log('[Vectorizer] Search results:', searchResults.matches);

		if (!searchResults.matches || searchResults.matches.length === 0) {
			return 'Không tìm thấy bối cảnh liên quan trong các chương trước.';
		}

		const contextSnippets = searchResults.matches
			.map((match) => `- (Chapter ${match.metadata!.chapterNumber}): "${match.metadata!.text}"`)
			.join('\n');

		return contextSnippets;
	} catch (error) {
		console.error(`[Vectorizer] Failed to retrieve context for query "${query}":`, error);
		throw new Error('Failed to retrieve context.');
	}
}
