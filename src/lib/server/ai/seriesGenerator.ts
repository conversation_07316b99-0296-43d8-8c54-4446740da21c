import { type OpenAIClient } from '$lib/server/ai/openAI';
import { fillTemplate } from '$lib/utils/template';
import { GenerateContentResponseUsageMetadata, GoogleGenAI, Type } from '@google/genai';
import type <PERSON>A<PERSON> from 'openai';
import { genres, type GenreKey } from './genres';

type NewSeriesOutput = {
	title: string;
	content: string;
	semantic_queries: string[];
	metadata: {
		characters: string[];
		skills: string[];
		tags: string[];
		keywords: string[];
		locations: string[];
		memoryType: string;
	};

	theme: string;
	idea: string;
	summary: string;
	outline: string;
	userPrompt: string;

	usageMetadata?: GenerateContentResponseUsageMetadata;
};

/**
 * Generates Chapter 1 of a new story series for a given genre.
 * @param genreKey - The key of the genre to generate (e.g., 'rebirth').
 * @param env - The Cloudflare platform environment.
 * @returns The complete data for the first chapter.
 */
export async function generateNewSeries(
	openAIClient: OpenAIClient,
	genAIClient: <PERSON>GenAI,
	genreKey: GenreKey
): Promise<NewSeriesOutput> {
	const { name: genreName, ideaGenerationThemes, genesisPrompts, mainModel, helperModel } = genres[genreKey];

	console.log('[AI Helper] Generating new story idea...');

	console.log('[AI Helper] Selecting theme...');
	const inspirationTheme = ideaGenerationThemes[Math.floor(Math.random() * ideaGenerationThemes.length)];
	console.log('[AI Helper] Selected theme:', inspirationTheme);

	console.log('[AI Helper] Generating idea prompt...');
	const ideaGenerationPrompt = fillTemplate(genesisPrompts.ideaGeneration, {
		theme: inspirationTheme,
		genreName
	});
	console.log('[AI Helper] Generated idea prompt:', ideaGenerationPrompt);

	console.log('[AI Helper] Generating idea...');
	const {
		success: ideaGenerationSuccess,
		message: ideaGenerationMessage,
		data: ideaGenerationResponse
	} = await openAIClient.completions<OpenAI.Chat.Completions.ChatCompletion>({
		model: helperModel,
		messages: [{ role: 'user', content: ideaGenerationPrompt }]
	});

	const ideaContent = ideaGenerationResponse.choices[0].message.content?.trim();

	if (!ideaGenerationSuccess || !ideaContent) {
		console.error('[AI Helper] Failed to generate idea:', ideaGenerationMessage);
		throw new Error('Failed to generate story idea.');
	}
	console.log('[AI Helper] Generated idea:', ideaContent);

	console.log('[AI Helper] Generating summary prompt...');
	const summarizeIdeaPrompt = fillTemplate(genesisPrompts.summarizeIdea, {
		storyIdea: ideaContent
	});
	console.log('[AI Helper] Generated summary prompt:', summarizeIdeaPrompt);

	console.log('[AI Helper] Generating summary...');
	const {
		success: summaryGenerationSuccess,
		message: summaryGenerationMessage,
		data: summaryGenerationResponse
	} = await openAIClient.completions<OpenAI.Chat.Completions.ChatCompletion>({
		model: helperModel,
		messages: [{ role: 'user', content: summarizeIdeaPrompt }]
	});

	const summaryContent = summaryGenerationResponse.choices[0].message.content?.trim();

	if (!summaryGenerationSuccess || !summaryContent) {
		console.error('[AI Helper] Failed to generate summary:', summaryGenerationMessage);
		throw new Error('Failed to generate story summary.');
	}
	console.log('[AI Helper] Generated summary:', summaryContent);

	console.log('[AI Helper] Generating outline prompt...');
	const outlineGenerationPrompt = fillTemplate(genesisPrompts.outlineGeneration, {
		storySummary: summaryContent
	});
	console.log('[AI Helper] Generated outline prompt:', outlineGenerationPrompt);

	console.log('[AI Helper] Generating outline...');
	const {
		success: outlineGenerationSuccess,
		message: outlineGenerationMessage,
		data: outlineGenerationResponse
	} = await openAIClient.completions<OpenAI.Chat.Completions.ChatCompletion>({
		model: helperModel,
		messages: [{ role: 'user', content: outlineGenerationPrompt }]
	});

	const outlineContent = outlineGenerationResponse.choices[0].message.content?.trim();

	if (!outlineGenerationSuccess || !outlineContent) {
		console.error('[AI Helper] Failed to generate outline:', outlineGenerationMessage);
		throw new Error('Failed to generate story outline.');
	}
	console.log('[AI Helper] Generated outline:', outlineContent);

	console.log('[AI Helper] Generating user prompt...');
	const userPrompt = fillTemplate(genesisPrompts.userPromptChapter1, {
		storyTheme: inspirationTheme,
		storySummary: summaryContent,
		storyOutline: outlineContent
	});
	console.log('[AI Helper] Generated user prompt:', userPrompt);

	console.log('[AI Story Generator] Generating Chapter 1...');
	// const {
	// 	success: chapterGenerationSuccess,
	// 	message: chapterGenerationMessage,
	// 	data: chapterGenerationResponse
	// } = await openAIClient.completions<ParsedChatCompletion<StoryOutput>>({
	// 	model: mainModel,
	// 	messages: [
	// 		{ role: 'developer', content: genesisPrompts.systemPromptChapter1 },
	// 		{ role: 'user', content: userPrompt }
	// 	],
	// 	response_format: zodResponseFormat(StorySchema, 'output')
	// });

	const chapterGenerationResponse = await genAIClient.models.generateContent({
		model: mainModel,
		config: {
			systemInstruction: genesisPrompts.systemPromptChapter1,
			responseMimeType: 'application/json',
			responseSchema: {
				type: Type.OBJECT,
				properties: {
					title: { type: Type.STRING },
					content: { type: Type.STRING },
					semantic_queries: { type: Type.ARRAY, items: { type: Type.STRING } },
					metadata: {
						type: Type.OBJECT,
						properties: {
							characters: { type: Type.ARRAY, items: { type: Type.STRING } },
							skills: { type: Type.ARRAY, items: { type: Type.STRING } },
							tags: { type: Type.ARRAY, items: { type: Type.STRING } },
							keywords: { type: Type.ARRAY, items: { type: Type.STRING } },
							locations: { type: Type.ARRAY, items: { type: Type.STRING } },
							memoryType: { type: Type.STRING }
						},
						required: ['characters', 'skills', 'tags', 'keywords', 'locations', 'memoryType']
					}
				},
				required: ['title', 'content', 'semantic_queries', 'metadata']
			},
			candidateCount: 1,
			maxOutputTokens: 4096 * 3
		},

		contents: userPrompt
	});

	console.log('[AI Story Generator] Generated Chapter 1 (Gemini):', chapterGenerationResponse);

	// const chapterContent = chapterGenerationResponse.choices[0].message.content?.trim();

	if (!chapterGenerationResponse.text) {
		console.error('[AI Story Generator] Failed:', chapterGenerationResponse.text);
		throw new Error('Failed to generate story.');
	}
	console.log('[AI Story Generator] Generated Chapter 1 usage:', chapterGenerationResponse.usageMetadata);

	const storyData = <NewSeriesOutput>JSON.parse(chapterGenerationResponse.text);

	return {
		...storyData,
		theme: inspirationTheme,
		idea: ideaContent,
		summary: summaryContent,
		outline: outlineContent,
		userPrompt,
		usageMetadata: chapterGenerationResponse.usageMetadata
	};
}
