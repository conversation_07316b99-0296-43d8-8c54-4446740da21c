import type { AxiosError, AxiosInstance } from 'axios';
import axios from 'axios';
import type OpenA<PERSON> from 'openai';

interface ApiResponse<T = unknown> {
	success: boolean;
	statusCode: number;
	message: string;
	data: T;
	timestamp: string;
	path: string;
}

/**
 * Creates a new API client instance configured to talk to our proxy.
 * This is a "factory function".
 * @param proxyUrl The full URL of the self-hosted proxy.
 * @param proxySecret The secret to authenticate with the proxy.
 * @returns An object with methods to call the AI APIs.
 */
export function initializeOpenAIClient({ baseURL, secret }: { baseURL: string; secret: string }) {
	const apiInstance: AxiosInstance = axios.create({
		baseURL, // Set the base URL once
		headers: {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${secret}` // Set the auth header once
		},
		timeout: 300000 // Set a generous timeout (e.g., 60 seconds)
	});

	apiInstance.interceptors.response.use(
		// The first function handles successful responses (2xx status codes)
		// We just pass it through directly.
		(response) => {
			return response;
		},

		// The second function handles any failed responses
		(error: AxiosError) => {
			// Log the error in a structured way on the server
			if (error.response) {
				// The request was made and the server responded with a status code
				// that falls out of the range of 2xx
				console.error(`[API Client Error] Status: ${error.response.status}`);
				console.error('[API Client Error] Data: ', error.response.data);
			} else if (error.request) {
				// The request was made but no response was received
				console.error('[API Client Error] No response received:', error.request);
			} else {
				// Something happened in setting up the request that triggered an Error
				console.error('[API Client Error] Error during request setup:', error.message);
			}

			// It's crucial to re-throw the error so that the original calling function
			// knows that the request failed and can stop its execution.
			return Promise.reject(error);
		}
	);

	return {
		completions: async <T>(
			params: OpenAI.Chat.Completions.ChatCompletionCreateParamsNonStreaming
		): Promise<ApiResponse<T>> => {
			const { data } = await apiInstance.post<ApiResponse<T>>('/completions', params);
			return data;
		},
		embeddings: async (params: OpenAI.Embeddings.EmbeddingCreateParams): Promise<number[][]> => {
			const { data: responseData } = await apiInstance.post<ApiResponse<OpenAI.Embeddings.CreateEmbeddingResponse>>(
				'/embeddings',
				params
			);
			return responseData.data.data.map((item) => item.embedding);
		}
	};
}

export type OpenAIClient = ReturnType<typeof initializeOpenAIClient>;
