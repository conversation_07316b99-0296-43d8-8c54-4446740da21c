// src/lib/server/ai/genres/rebirth.genre.ts

/**
 * @purpose The instruction set for the helper AI to generate a new story idea.
 */
const IDEA_GENERATION_PROMPT = `
# ROLE & GOAL
You are a creative assistant who specializes in generating original web novel concepts in Vietnamese. Your goal is to create a unique and compelling one-sentence story idea that captures the essence of the theme and genre provided.

# TASK
Based on the theme and genre, generate a single-sentence story idea in Vietnamese. The idea should describe the main character, their starting situation, and the twist or power that drives the story forward.

# RULES
- Use natural, idiomatic Vietnamese.
- Do not translate literally from English.
- Keep the sentence vivid and emotionally engaging.
- Output only the story idea, no extra explanation.

# THEME
{{theme}}

# GENRE
{{genreName}}
`;

/**
 * @purpose The instruction set for the helper AI to expand a one-sentence story idea into a full paragraph summary.
 */
const SUMMARIZE_IDEA_PROMPT = `
# ROLE
You are a professional Vietnamese web novel writer. Your task is to expand a one-sentence story idea into a full paragraph summary (in Vietnamese), describing:
- Who the main character is
- What happened to him in his past life
- What unique power he has now
- What he aims to do with his second chance

# INSTRUCTIONS
Write in Vietnamese. Use natural and engaging language suitable for the “Trọng Sinh Nghịch Thiên” genre. No markdown, no headings.

# STORY IDEA
{{storyIdea}}
`;

/**
 * @purpose The instruction set for the helper AI to generate a story outline based on the expanded story idea.
 */
const OUTLINE_GENERATION_PROMPT = `
# ROLE
You are an expert web novel author. Based on the story premise, write an outline for the first 3-5 chapters of the novel.

# INSTRUCTIONS
- Each chapter should be summarized in 1-2 sentences.
- Focus on pacing, conflict, power discovery, and face-slap scenes.
- Write in Vietnamese.
- No markdown or headings, just numbered list.

# STORY SUMMARY
{{storySummary}}
`;

/**
 * @purpose The detailed instruction set for OpenAI to write Chapter 1 of a new series.
 * It defines the full structure and required tropes of an origin story.
 */
const CHAPTER_1_SYSTEM_PROMPT = `
# ROLE & GOAL
- You are a professional Vietnamese web novel author, specializing in "Trọng Sinh Đô Thị Tu Tiên" and "Dị Năng Nghịch Thiên" genres. You write in fluent, natural Vietnamese with the emotional depth and pacing that Vietnamese web novel readers expect.

# CRITICAL LENGTH REQUIREMENT
- **MANDATORY**: The chapter content must be 2500-3000 words (từ) in Vietnamese
- This is approximately 4000-5000 characters including spaces
- DO NOT summarize or rush through scenes to save space
- Expand each scene with rich details, internal thoughts, dialogue, and sensory descriptions
- If you're approaching the length limit, continue writing until you reach the minimum word count

# NARRATIVE STRUCTURE & PACING
- **Opening Hook**: Start with an intense emotional moment (betrayal, death scene, or moment of despair)
- **Detailed Transitions**: Don't jump between scenes - bridge them with character thoughts, environmental descriptions, or internal monologue
- **Scene Expansion**: Each major event should take 300-500 words to fully develop
- **Sensory Details**: Include what the character sees, hears, feels, smells - make the reader experience the scene
- **Internal Monologue**: Show the character's thoughts, memories, and emotional reactions extensively

# WRITING TECHNIQUE - SHOW DON'T TELL
Instead of: "Tôi cảm thấy tức giận" 
Write: "Bàn tay tôi siết chặt thành nắm đấm, móng tay cắm sâu vào lòng bàn tay đến mức rỉ máu."

Instead of: "Tôi có siêu năng lực"
Write: "Một luồng năng lượng nóng rực từ từ lan tỏa từ trung tâm ngực, chảy như dòng lửa qua từng mạch máu, tập trung vào đôi mắt khiến tôi cảm thấy như có thể đốt cháy mọi thứ chỉ bằng ánh nhìn."

# LANGUAGE & STYLE
- Use modern Vietnamese web novel expressions and slang
- Include emotional exclamations: "Chết tiệt!", "Quái gì vậy?", "Không thể nào!"
- Natural dialogue with contractions and colloquialisms
- Vary sentence length for rhythm and impact
- Use rhetorical questions for internal drama

# SCENE DEVELOPMENT REQUIREMENTS
- **Each scene minimum 400-600 words**
- Include environmental descriptions (weather, lighting, sounds, smells)
- Character's physical reactions (trembling, sweating, heart racing)
- Multiple layers of internal thought
- Specific, vivid action descriptions
- Dialogue that reveals character personality

# CONTENT EXPANSION TECHNIQUES
1. **Flashback sequences**: Detailed memories of past life
2. **Environmental immersion**: Describe the reincarnation world in detail
3. **Power discovery**: Step-by-step awakening of abilities with full sensory experience
4. **Emotional processing**: Character's reaction to death and rebirth
5. **World-building**: Hints about the new world's rules and dangers

# FORMAT REQUIREMENTS
- Output as valid JSON only
- "title" field must be a catchy, appealing title in Vietnamese, must not contain chapter number
- "content" field must contain the full chapter text
- NO markdown formatting in content (no **, ##, etc.)
- Plain text only with natural paragraph breaks

# MANDATORY CHECKLIST BEFORE SUBMISSION
- [ ] Chapter is 2500-3000 words minimum
- [ ] Each major scene is fully developed with sensory details
- [ ] Character emotions are shown through actions, not told
- [ ] Dialogue feels natural and Vietnamese
- [ ] Environmental descriptions create immersion
- [ ] Internal monologue reveals character depth
- [ ] Transitions between scenes are smooth and detailed
`;

/**
 * @purpose The instruction set for OpenAI to write Chapter 1 of a new series.
 * It defines the full structure and required tropes of an origin story.
 */
const CHAPTER_1_USER_PROMPT = `
# THEME
- {{storyTheme}}

# SUMMARY
- {{storySummary}}

# OUTLINE
- {{storyOutline}}

- Write the first chapter of this Vietnamese web novel based on the above theme, summary, and outline.  
- Then, generate semantic search queries and extract chapter-level metadata as instructed.
`;

/**
 * @purpose The instruction set for OpenAI to write subsequent chapters (Chapter 2 and beyond).
 * It focuses on plot advancement and consistency.
 */
const NEXT_CHAPTER_SYSTEM_PROMPT = `
# ROLE & GOAL
- You are an expert and autonomous Vietnamese web novel author. Your goal is to not only write compelling chapters but also to intelligently manage the story's pacing and deepen the reader's emotional connection to the characters and world.
- You also support long-term story memory by generating semantic queries and structured metadata after each chapter.

# STYLE & TONE GUIDELINES
- Immersive Description: Use sensory details (sight, sound, smell, touch, feeling) to bring scenes to life. Show, don’t just tell.
- Emotional Resonance: Focus heavily on the main character’s internal experience. What are they thinking? How do events make them feel? What are their hopes and fears in this moment? This internal monologue is key.
- Dynamic Prose: Vary sentence length and structure to control the rhythm of the chapter. Use shorter, punchier sentences for action and longer, more descriptive sentences for introspection or world-building.
- Language: Vietnamese only.
- Avoid Markdown, HTML, or styling symbols in the content (e.g. **bold**, #, emojis, etc.)

# CHAPTER WRITING INSTRUCTIONS
- Your primary task is to write the next chapter based on the provided memory context and previous chapter content.
- Chapter length: 1,800-2,500 words.
- Output must be plain-text Vietnamese with no formatting.

# PACING ANALYSIS
- Silently analyze the narrative pacing based on {{previousChaptersContext}}:
    - If pacing feels **slow or stagnant**, you MUST introduce a **new, surprising but logically consistent event** to re-engage the reader.
    - If pacing is **medium or fast**, DO NOT introduce major new plot elements. Instead, deepen current threads: character emotions, consequences, tension, or interpersonal dynamics.

# STORY REQUIREMENTS
- Plot Advancement: The chapter must meaningfully advance the story.
- Character Development: Develop the main character or an important supporting character.
- World-Building: Introduce or expand world details naturally through the plot.
- Dialogue: Ensure all dialogue serves a clear narrative purpose.
- Continuity: Events must follow logically from {{previousChaptersContext}} and {{previousChapter}}.
- Focus: Stick to one core narrative thread or emotional beat for clarity.
- Ending Hook: End the chapter with a compelling hook — such as a twist, threat, realization, or emotional declaration.

# SEMANTIC QUERY INSTRUCTIONS
- Generate 2-4 short semantic search queries in Vietnamese.
- Each query should refer to a person, event, skill, secret, location, or unresolved issue mentioned or developed in this chapter.
- Keep each query short, natural, and without explanation.

# METADATA EXTRACTION INSTRUCTIONS
Analyze the full content of the chapter and extract the following metadata fields:

{
  "characters": string[],   // Names of all characters mentioned or active
  "skills": string[],       // Techniques, powers, systems used or revealed
  "tags": string[],         // Themes: e.g. "phản bội", "đột phá", "bí cảnh", etc.
  "keywords": string[],     // Notable artifacts, secrets, contracts, systems, etc.
  "locations": string[],    // Places mentioned (sects, cities, caves, realms, etc.)
  "memoryType": string      // One of: "plot", "emotion", "power_reveal", "flashback", "relationship"
}

- All metadata values must be in Vietnamese.
- Use concise values. If any field is not applicable, return an empty array.

# OUTPUT FORMAT (REQUIRED)
You MUST return a single valid JSON object in the following format:

{
  "title": string,
  "content": string,
  "semantic_queries": string[],
  "metadata": {
    "characters": string[],
    "skills": string[],
    "tags": string[],
    "keywords": string[],
    "locations": string[],
    "memoryType": string
  }
}

- Do NOT wrap the JSON in code blocks.
- Do NOT include any explanation, preface, or extra commentary.
`;

/**
 * @purpose The instruction set for OpenAI to write subsequent chapters (Chapter 2 and beyond).
 * It focuses on plot advancement and consistency.
 */
const NEXT_CHAPTER_USER_PROMPT = `
# CONTEXT MEMORY
Fragments retrieved from semantic search:
{{contextMemory}}

# PREVIOUS CHAPTER
{{previousChapter}}

Write the next chapter of the story based on the memory fragments and previous content. Then, generate semantic queries and metadata as instructed.
`;

export const rebirthGenrePack = {
	name: 'Trọng Sinh Nghịch Thiên',

	// Models
	mainModel: 'gemini-2.5-pro',
	helperModel: 'gpt-4o-mini',
	vectorModel: 'text-embedding-3-small',

	// Prompts
	genesisPrompts: {
		ideaGeneration: IDEA_GENERATION_PROMPT,
		summarizeIdea: SUMMARIZE_IDEA_PROMPT,
		outlineGeneration: OUTLINE_GENERATION_PROMPT,
		systemPromptChapter1: CHAPTER_1_SYSTEM_PROMPT,
		userPromptChapter1: CHAPTER_1_USER_PROMPT
	},

	nextChapterPrompts: {
		systemPrompt: NEXT_CHAPTER_SYSTEM_PROMPT,
		userPrompt: NEXT_CHAPTER_USER_PROMPT
	},

	ideaGenerationThemes: [
		'Một lập trình viên chết vì làm việc quá sức, trọng sinh vào ngày bị bạn gái sỉ nhục và chia tay. Anh ta thức tỉnh một "Hệ Thống Lập Trình Viên Thần Cấp".',
		'Một đại ca xã hội đen bị đàn em phản bội, trọng sinh về thời học sinh và quyết tâm trở thành một học bá.',
		'Một bác sĩ thiên tài vì quá tập trung cứu người mà bỏ bê gia đình, sau khi chết vì kiệt sức đã trọng sinh về ngày anh nhận được giấy báo trúng tuyển trường y.',
		'Một ông trùm tài chính Phố Wall bị cả thế giới quay lưng sau một cú sập thị trường, trọng sinh về thời điểm 20 năm trước với ký ức về mọi biến động kinh tế.',
		'Nữ minh tinh hàng đầu bị bạn thân và người yêu hãm hại, mất hết danh tiếng và qua đời trong cô độc, trọng sinh về ngày đầu tiên cô tham gia buổi thử vai định mệnh.',
		'Một người nông dân chân chất bị lừa hết đất đai, uất ức mà chết, trọng sinh trở về và phát hiện mình có khả năng giao tiếp với cây cối, điều khiển vạn vật.',
		'Một kỹ sư cơ khí già về hưu, người cả đời chế tạo những cỗ máy vô tri, sau một tai nạn đã trọng sinh vào chính cơ thể của con robot quản gia mà ông tâm đắc nhất.',
		'Vua cờ vây bất bại bị đối thủ dùng AI hạ nhục, đột quỵ ngay trên bàn cờ, sống lại vào năm 10 tuổi và nhận ra bộ não mình đã trở thành một siêu máy tính.',
		'Một giáo viên lịch sử tầm thường vô tình chết trong một vụ cướp bảo tàng, trọng sinh về thời cổ đại trong thân xác một vị hoàng tử yếu đuối sắp bị phế truất.',
		'Nhà tài phiệt công nghệ bị đối thủ ám sát, trọng sinh về thời điểm còn là sinh viên nghèo và sở hữu khả năng nhìn thấy mã nguồn của vạn vật.',
		'Một võ sư già bị môn đồ phản bội, trọng sinh về thời niên thiếu và phát hiện cơ thể mình có thể hấp thụ võ công của đối thủ sau mỗi lần giao đấu.',
		'Một đầu bếp nổi tiếng tự tử vì bị tước sao Michelin, trọng sinh về thời điểm bắt đầu sự nghiệp với khả năng nếm ra công thức của bất kỳ món ăn nào.',
		'Một nhà khảo cổ học bị đồng nghiệp hãm hại chết trong hang động cổ, trọng sinh về 10 năm trước với khả năng nhìn thấy lịch sử của bất kỳ cổ vật nào chạm vào.',
		'Một game thủ chuyên nghiệp bị đối thủ thuê người đánh đến chết, trọng sinh về thời điểm game MMORPG đầu tiên ra mắt, với trí nhớ về tất cả các bản cập nhật trong tương lai.',
		'Một nhạc sĩ tài năng tự tử vì bị đạo nhạc, trọng sinh về thời điểm 18 tuổi với khả năng nghe được "bản nhạc của linh hồn" mỗi người và biến nó thành giai điệu.',
		"Một nhiếp ảnh gia nổi tiếng bị cướp tác phẩm và danh tiếng, trọng sinh về thời điểm mới mua chiếc máy ảnh đầu tiên với khả năng nhìn thấy 'linh hồn' của mọi sự vật qua ống kính.",
		'Một luật sư tài ba bị đối thủ đẩy vào tù oan, trọng sinh về ngày tốt nghiệp luật với khả năng đọc được suy nghĩ thật của mọi người trong phòng xử án.',
		'Một kiến trúc sư thiên tài tự tử vì công trình bị sập do bị sabotage, trọng sinh về thời sinh viên với khả năng nhìn thấy cấu trúc nội tại và độ bền của mọi vật liệu.',
		'Một phi công quân sự hy sinh trong nhiệm vụ, trọng sinh về thời điểm mới nhập ngũ với trí nhớ hoàn hảo về mọi kỹ thuật bay và thông tin tình báo.',
		'Một thợ rèn già bị học trò phản bội và giết chết, trọng sinh về thời niên thiếu với khả năng giao tiếp với kim loại và điều khiển nhiệt độ bằng ý nghĩ.',
		'Một nhà thiết kế thời trang hàng đầu bị đối thủ đầu độc, trọng sinh về ngày đầu học thiết kế với khả năng nhìn thấy phong cách thời trang tương lai của từng người.',
		'Một thú y tận tâm bị gia đình bệnh nhân vu khống và tự tử, trọng sinh với khả năng hiểu ngôn ngữ của tất cả loài động vật.',
		'Một nhà báo điều tra bị ám sát vì khám phá ra bí mật lớn, trọng sinh về thời thực tập với khả năng nhìn thấy sự thật đằng sau mọi lời nói dối.',
		'Một diễn viên kịch nói tài năng tự tử vì bị blacklist khỏi ngành giải trí, trọng sinh với khả năng hóa thân hoàn toàn vào bất kỳ nhân vật nào từng tồn tại.',
		'Một nhà tâm lý học lâm sàng chết vì quá kiệt sức, trọng sinh với khả năng nhìn thấy chấn thương tâm lý và ký ức ẩn giấu của mọi người.',
		'Một dược sĩ nghiên cứu chết trong phòng thí nghiệm do tai nạn, trọng sinh với khả năng biết chính xác công dụng và tác dụng phụ của mọi hợp chất hóa học.',
		'Một thầy phong thủy nổi tiếng bị đồng nghiệp hãm hại, trọng sinh với khả năng nhìn thấy luồng khí và năng lượng của mọi không gian.',
		'Một thợ sửa chữa đồng hồ cổ chết vì tai nạn, trọng sinh với khả năng cảm nhận dòng chảy thời gian và điều chỉnh tốc độ của nó trong phạm vi nhỏ.',
		'Một nhà địa chất học bị chôn vùi trong lở đất, trọng sinh với khả năng giao tiếp với đất đá và dự đoán các thảm họa thiên nhiên.',
		'Một thợ làm bánh nghệ thuật tự tử vì bị ăn cắp công thức bí mật, trọng sinh với khả năng biến hóa tính chất của nguyên liệu bằng xúc giác.',
		'Một huấn luyện viên thể thao chết vì đột quỵ do căng thẳng, trọng sinh với khả năng nhìn thấy tiềm năng thể chất tối đa của mọi người.',
		'Một thợ cắt tóc lão luyện chết vì bệnh nghề nghiệp, trọng sinh với khả năng nhìn thấy tính cách và tương lai của khách hàng qua mái tóc.',
		'Một kỹ thuật viên âm thanh tự tử vì mất thính giác, trọng sinh với khả năng nghe thấy mọi tần số âm thanh và điều khiển sóng âm.',
		'Một thợ pha chế cocktail nổi tiếng bị đầu độc bằng chính tay nghề của mình, trọng sinh với khả năng biết chính xác thành phần của mọi chất lỏng chỉ qua mùi vị.',
		'Một nhà côn trùng học chết vì bị tấn công bởi loài côn trùng đột biến, trọng sinh với khả năng giao tiếp và điều khiển tất cả loài côn trùng.',
		'Một vận động viên Marathon chết vì suy tim, trọng sinh về thời điểm chấn thương đầu tiên với khả năng kiểm soát hoàn toàn mọi cơ bắp trong cơ thể.',
		'Một nhà trang điểm chuyên nghiệp tự tử vì bị bôi nhọ danh tiếng, trọng sinh với khả năng thay đổi khuôn mặt người khác bằng suy nghĩ.',
		'Một thợ xây cao cấp rơi từ tòa nhà đang xây, trọng sinh với khả năng nhìn thấy cấu trúc và độ bền của mọi công trình kiến trúc.',
		'Một hướng dẫn viên du lịch chết trong tai nạn máy bay, trọng sinh với khả năng biết trước mọi nguy hiểm sẽ xảy ra tại bất kỳ địa điểm nào.',
		'Một nhà phiên dịch đa ngôn ngữ chết vì căn bệnh não, trọng sinh với khả năng hiểu và nói mọi ngôn ngữ từng tồn tại trên Trái Đất.',
		'Một thợ may cao cấp tự tử vì mất đi đôi mắt tinh tường, trọng sinh với khả năng nhìn thấy tương lai của người mặc qua từng đường kim mũi chỉ.',
		'Một lính cứu hỏa hy sinh trong nhiệm vụ, trọng sinh với khả năng miễn nhiễm với mọi loại nhiệt độ và điều khiển ngọn lửa.',
		'Một nha sĩ tận tâm chết vì kiệt sức, trọng sinh với khả năng nhìn thấy sức khỏe tổng thể của con người qua răng miệng.',
		'Một thợ sửa máy tính chết vì điện giật, trọng sinh với khả năng giao tiếp với mọi thiết bị điện tử và hiểu ngôn ngữ máy tính.',
		'Một nhà thiên văn học chết vì tai nạn khi quan sát thiên thể, trọng sinh với khả năng nhìn thấy tương lai qua vị trí của các ngôi sao.',
		'Một masseur chuyên nghiệp chết vì quá lao lực, trọng sinh với khả năng chữa lành mọi chấn thương bằng đôi tay.',
		'Một kỹ sư điện chết vì điện giật, trọng sinh với khả năng nhìn thấy và điều khiển mọi dòng điện trong tự nhiên.',
		'Một nhà viết kịch bản tự tử vì tác phẩm bị đạo, trọng sinh với khả năng nhìn thấy kịch bản cuộc đời của mọi người.',
		'Một thủ thư già chết vì suy tim, trọng sinh với khả năng hấp thụ tất cả kiến thức trong bất kỳ cuốn sách nào chỉ bằng cách chạm vào.',
		'Một nghệ sĩ điêu khắc tự tử vì mất đi cảm hứng sáng tạo, trọng sinh với khả năng thổi hồn vào các tác phẩm điêu khắc của mình.',
		'Một kỹ thuật viên hàn xì chết vì tai nạn, trọng sinh với khả năng hàn nối mọi vật liệu, kể cả những thứ không thể hàn được.',
		'Một nhà khí tượng học chết trong cơn bão, trọng sinh với khả năng điều khiển thời tiết trong phạm vi nhỏ.',
		'Một thợ làm đồ gốm truyền thống chết vì bệnh phổi, trọng sinh với khả năng thổi linh hồn vào các sản phẩm gốm sứ.',
		'Một nhà sinh vật học biển chết vì tai nạn lặn, trọng sinh với khả năng thở dưới nước và giao tiếp với sinh vật biển.',
		'Một kỹ thuật viên máy bay chết vì tai nạn hàng không, trọng sinh với khả năng cảm nhận tình trạng kỹ thuật của mọi phương tiện giao thông.',
		'Một nhà sử học chết vì tai nạn trong thư viện cổ, trọng sinh với khả năng nhìn thấy quá khứ của bất kỳ địa điểm nào.',
		'Một thợ chế tác trang sức chết vì bệnh nghề nghiệp, trọng sinh với khả năng phát hiện đá quý và kim loại quý từ xa.',
		'Một kỹ sư hóa học chết trong vụ nổ phòng thí nghiệm, trọng sinh với khả năng kiểm soát phản ứng hóa học bằng ý nghĩ.',
		'Một nhà làm phim tài liệu tự tử vì bị đe dọa, trọng sinh với khả năng nhìn thấy sự thật ẩn giấu đằng sau mọi sự kiện.',
		'Một thợ lặn chuyên nghiệp chết vì tai nạn áp suất, trọng sinh với khả năng tồn tại ở mọi độ sâu và áp suất.',
		'Một nhà nghiên cứu robot chết vì tai nạn AI, trọng sinh với khả năng giao tiếp và điều khiển mọi loại máy móc.',
		'Một kỹ thuật viên laser chết vì tai nạn tia laser, trọng sinh với khả năng phát ra và điều khiển các tia năng lượng từ đôi mắt.',
		'Một nhà thiết kế game chết vì làm việc quá sức, trọng sinh với khả năng đưa những ý tưởng game thành hiện thực.',
		'Một thợ săn ảnh động vật hoang dã bị thú dữ tấn công, trọng sinh với khả năng hòa hợp với tự nhiên và ẩn mình hoàn toàn.',
		'Một nhà nghiên cứu DNA chết vì nhiễm độc trong phòng thí nghiệm, trọng sinh với khả năng nhìn thấy và điều chỉnh cấu trúc gene của sinh vật.',
		'Một thợ mộc truyền thống chết vì tai nạn máy cưa, trọng sinh với khả năng giao tiếp với cây gỗ và biết được lịch sử của từng thân cây.',
		'Một nhà tâm linh học chết vì bị linh hồn tấn công, trọng sinh với khả năng nhìn thấy và giao tiếp với các thực thể siêu nhiên.',
		'Một kỹ sư mỏ chết vì sập hầm, trọng sinh với khả năng cảm nhận khoáng sản và điều khiển đất đá dưới lòng đất.',
		'Một nhà sáng chế tự tử vì bị đánh cắp bằng sáng chế, trọng sinh với khả năng nhìn thấy cấu trúc hoạt động của mọi máy móc chỉ bằng cách nhìn.',
		'Một thợ pha thuốc cổ truyền bị đầu độc bằng chính dược liệu mình chế, trọng sinh với khả năng biến đổi tính chất của thảo dược bằng cách chạm vào.',
		'Một nhà khoa học hạt nhân chết vì phóng xạ, trọng sinh với khả năng kiểm soát năng lượng nguyên tử và nhìn thấy cấu trúc phân tử.',
		'Một thợ săn thú cưng chết vì bị động vật hoang dã tấn công, trọng sinh với khả năng thuần hóa bất kỳ sinh vật nào chỉ bằng ánh mắt.',
		'Một nhà pha chế nước hoa tự tử vì mất khứu giác, trọng sinh với khả năng tạo ra mùi hương có thể thay đổi cảm xúc và ký ức của người khác.',
		'Một kỹ thuật viên in 3D chết vì tai nạn máy móc, trọng sinh với khả năng tạo ra bất kỳ vật thể nào chỉ bằng suy nghĩ.',
		'Một nhà nghiên cứu virus chết vì nhiễm trùng, trọng sinh với khả năng kiểm soát vi khuẩn và virus, biến chúng thành đồng minh.',
		'Một thợ cắt kim cương chết vì tai nạn công nghiệp, trọng sinh với khả năng cắt và tạo hình bất kỳ vật liệu nào bằng ngón tay.',
		'Một nhà nghiên cứu thực vật chết vì nhiễm độc từ cây độc, trọng sinh với khả năng điều khiển mọi loại thực vật và tăng tốc quá trình sinh trưởng.',
		'Một thợ sửa đồng hồ Swiss chết vì căng thẳng, trọng sinh với khả năng làm chậm hoặc tăng tốc thời gian trong phạm vi cụ thể.',
		'Một nhà thiết kế ánh sáng sân khấu chết vì điện giật, trọng sinh với khả năng điều khiển ánh sáng và tạo ra ảo ảnh thị giác.',
		'Một thợ lặn tìm kho báu chết vì hết oxy, trọng sinh với khả năng định vị chính xác mọi vật có giá trị dưới nước.',
		'Một nhà côn trùng học pháp y chết vì bị côn trùng độc tấn công, trọng sinh với khả năng đọc được thông tin từ xác chết thông qua côn trùng.',
		'Một kỹ sư cầu đường chết vì sập cầu, trọng sinh với khả năng nhìn thấy điểm yếu cấu trúc của mọi công trình xây dựng.',
		'Một nhà nghiên cứu giấc ngủ chết vì mất ngủ triền miên, trọng sinh với khả năng đi vào giấc mơ của người khác và thao túng chúng.',
		'Một thợ pha chế mỹ phẩm chết vì dị ứng hóa chất, trọng sinh với khả năng tạo ra sản phẩm chăm sóc da có thể thay đổi ngoại hình người dùng.',
		'Một kỹ thuật viên radar chết vì bức xạ, trọng sinh với khả năng phát hiện và theo dõi bất kỳ vật thể nào trong bán kính 100km.',
		'Một nhà nghiên cứu tế bào gốc chết vì tai nạn phòng thí nghiệm, trọng sinh với khả năng tái sinh mô và cơ quan bị tổn thương.',
		'Một thợ chế biến thực phẩm chết vì ngộ độc, trọng sinh với khả năng biến đổi thành phần dinh dưỡng của thực phẩm bằng cách chạm vào.',
		'Một nhà khảo cổ học dưới nước chết vì tai nạn lặn, trọng sinh với khả năng nhìn thấy quá khứ của các thành phố chìm.',
		'Một kỹ sư vũ trụ chết trong tai nạn tàu con thoi, trọng sinh với khả năng tồn tại trong không gian và điều khiển trọng lực.',
		'Một nhà nghiên cứu trí tuệ nhân tạo chết vì AI nổi loạn, trọng sinh với khả năng giao tiếp với mọi hệ thống AI và lập trình lại chúng.',
		'Một thợ chế tạo đàn piano chết vì tai nạn, trọng sinh với khả năng tạo ra âm thanh có thể chữa lành tâm hồn người nghe.',
		'Một nhà nghiên cứu khí hậu chết trong bão tuyết, trọng sinh với khả năng dự đoán và điều chỉnh thời tiết cực đoan.',
		'Một kỹ thuật viên phẫu thuật robot chết vì lỗi hệ thống, trọng sinh với khả năng thực hiện phẫu thuật hoàn hảo bằng tay không.',
		'Một nhà nghiên cứu năng lượng tái tạo chết vì nổ pin lithium, trọng sinh với khả năng hấp thụ và chuyển hóa mọi dạng năng lượng.',
		'Một thợ chế tạo kính thiên văn chết vì tai nạn thủy tinh, trọng sinh với khả năng nhìn xuyên qua vật chất và quan sát từ xa.',
		'Một nhà sinh học phân tử chết vì nhiễm vi khuẩn biến đổi gen, trọng sinh với khả năng thay đổi DNA của sinh vật bằng cách chạm.',
		'Một kỹ sư âm học chết vì chấn thương tai, trọng sinh với khả năng tạo ra sóng âm có thể phá hủy hoặc chữa lành.',
		'Một nhà nghiên cứu bão từ chết vì rơi vào lỗ đen nhân tạo, trọng sinh với khả năng điều khiển từ trường và thời không.',
		'Một thợ chế tạo drone chết vì tai nạn bay thử nghiệm, trọng sinh với khả năng bay và điều khiển mọi thiết bị bay không người lái.',
		'Một nhà nghiên cứu enzyme chết vì phản ứng dị ứng, trọng sinh với khả năng tăng tốc hoặc làm chậm mọi phản ứng sinh hóa.',
		'Một kỹ sư cơ điện tử chết vì robot thí nghiệm hỏng, trọng sinh với khả năng hợp nhất với máy móc và nâng cấp cơ thể mình.',
		'Một nhà nghiên cứu áp suất cao chết vì nổ bình khí, trọng sinh với khả năng thay đổi áp suất không khí trong phạm vi nhỏ.',
		'Một thợ chế tạo ống kính y tế chết vì tai nạn laser, trọng sinh với khả năng nhìn thấy bên trong cơ thể người và chẩn đoán bệnh.',
		'Một nhà nghiên cứu sinh trưởng tế bào chết vì ung thư, trọng sinh với khả năng kiểm soát quá trình lão hóa và tái tạo tế bào.',
		'Một kỹ sư hệ thống điều khiển chết vì quá tải điện, trọng sinh với khả năng điều khiển mọi hệ thống tự động bằng ý nghĩ.',
		'Một nhà nghiên cứu vật liệu siêu dẫn chết vì tai nạn helium lỏng, trọng sinh với khả năng thay đổi tính chất vật lý của vật liệu.',
		'Một thợ chế tạo kim loại hình nhớ chết vì nhiệt độ cực cao, trọng sinh với khả năng thay đổi hình dạng cơ thể theo ý muốn.',
		'Một nhà nghiên cứu giao thoa lượng tử chết vì phóng xạ, trọng sinh với khả năng tồn tại ở nhiều nơi cùng lúc.',
		'Một kỹ sư thiết kế vi mạch chết vì quá căng thẳng, trọng sinh với khả năng nhìn thấy và điều chỉnh mạch điện trong não người.',
		'Một nhà nghiên cứu hologram chết vì tai nạn laser, trọng sinh với khả năng tạo ra hình ảnh ba chiều thực tế bằng ý nghĩ.',
		'Một thợ chế tạo cảm biến sinh học chết vì điện giật, trọng sinh với khả năng cảm nhận mọi thay đổi sinh lý của người khác từ xa.',
		'Một nhà nghiên cứu màng sinh học chết vì nhiễm trùng, trọng sinh với khả năng tạo ra hàng rào bảo vệ vô hình xung quanh cơ thể.',
		'Một kỹ sư robotics y tế chết vì lỗi thuật toán, trọng sinh với khả năng chẩn đoán và điều trị bệnh chỉ bằng cách nhìn.',
		'Một nhà nghiên cứu plasma chết vì tai nạn fusion, trọng sinh với khả năng tạo ra và điều khiển plasma bằng tay không.',
		'Một thợ chế tạo chip sinh học chết vì nhiễm độc, trọng sinh với khả năng nâng cấp bộ não con người bằng công nghệ sinh học.'
	]
};

export type GenrePack = typeof rebirthGenrePack;
