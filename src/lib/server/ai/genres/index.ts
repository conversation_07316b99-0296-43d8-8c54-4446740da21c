// src/lib/server/ai/genres/index.ts
import { rebirthGenrePack } from './rebirth.genre';

/**
 * The central registry for all available story genres.
 * The keys ('rebirth') are used as identifiers in the database
 * and for API calls.
 */
export const genres = {
	rebirth: rebirthGenrePack
	// In future, if we want to add more genres, we can simply add them here.
	// 'xianxia': xianxiaGenrePack
};

// A type-safe way to refer to our genre keys.
export type GenreKey = keyof typeof genres;
