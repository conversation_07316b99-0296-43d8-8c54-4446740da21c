// src/lib/server/ai/hookExtractor.ts
import type { OpenAIClient } from '$lib/server/ai/openAI';
import { fillTemplate } from '$lib/utils/template';
import type OpenA<PERSON> from 'openai';
import type { GenrePack } from './genres/rebirth.genre';

interface HookExtractionParams {
	openaiClient: OpenAIClient;
	chapterContent: string;
	genrePack: GenrePack;
}

/**
 * Intelligently extracts the narrative hook from the end of a chapter using an AI.
 * Falls back to a simple substring method if the AI fails.
 * @param openaiClient - The OpenAI client instance.
 * @param chapterContent The full content of the chapter.
 * @param genrePack The genre pack containing the necessary prompt.
 * @returns The extracted hook string.
 */
export async function extractHook({ openaiClient, chapterContent, genrePack }: HookExtractionParams): Promise<string> {
	try {
		const prompt = fillTemplate(genrePack.extractHookPrompt, { content: chapterContent });

		const {
			success,
			message,
			data: aiResponse
		} = await openaiClient.completions<OpenAI.Chat.Completions.ChatCompletion>({
			model: genrePack.helperModel,
			messages: [{ role: 'user', content: prompt }]
		});

		if (!success) {
			console.error('[Hook Extractor] AI extraction failed:', message);
			return chapterContent.slice(-300);
		}

		const hook = aiResponse.choices[0].message.content;

		if (hook && hook.trim().length > 10) {
			console.log(`[Hook Extractor] Successfully extracted hook via AI: "${hook}"`);
			return hook.trim();
		} else {
			console.log(`[Hook Extractor] AI extracted hook is too short or empty. Falling back to simple method.`);
			throw new Error('[Hook Extractor] AI extracted hook is too short or empty.');
		}
	} catch (error) {
		console.error('[Hook Extractor] AI extraction failed, falling back to simple method.', error);
		throw new Error('[Hook Extractor] AI extraction failed.');
	}
}
