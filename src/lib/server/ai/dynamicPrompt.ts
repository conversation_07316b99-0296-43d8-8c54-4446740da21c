import type { OpenAIClient } from '$lib/server/ai/openAI';
import { fillTemplate } from '$lib/utils/template';
import type <PERSON><PERSON><PERSON> from 'openai';
import type { GenrePack } from './genres/rebirth.genre';

interface DynamicPromptParams {
	openaiClient: OpenAIClient;
	genrePack: GenrePack;
}

/**
 * Generates a dynamic user prompt using a theme and a prompt template from the provided genre pack.
 * This function is genre-agnostic, meaning it can generate ideas for any genre
 * as long as the genre pack provides the necessary templates and themes.
 *
 * @param openaiClient - The OpenAI client instance.
 * @param genrePack - The configuration object for a specific genre.
 * @returns A unique user prompt string to be used by the main story generator.
 */
export async function generateDynamicUserPrompt({ openaiClient, genrePack }: DynamicPromptParams): Promise<string> {
	const { ideaGenerationThemes, ideaGenerationPrompt, name: genreName, helperModel } = genrePack;

	const inspirationTheme = ideaGenerationThemes[Math.floor(Math.random() * ideaGenerationThemes.length)];

	const fullPrompt = fillTemplate(ideaGenerationPrompt, {
		theme: inspirationTheme,
		genreName
	});

	try {
		const {
			success,
			message,
			data: aiResponse
		} = await openaiClient.completions<OpenAI.Chat.Completions.ChatCompletion>({
			model: helperModel,
			messages: [{ role: 'user', content: fullPrompt }]
		});

		if (!success) {
			console.error('[AI Dynamic Prompt] Failed:', message);
			return inspirationTheme;
		}

		const content = aiResponse.choices[0].message.content?.trim();

		if (content) {
			console.log(`[AI Helper] Generated new idea: "${content}"`);
			return content;
		}
	} catch (error) {
		console.error('Workers AI failed to generate a dynamic prompt:', error);
	}

	console.log(`[AI Helper] Fallback: Using inspiration theme as prompt.`);
	return inspirationTheme;
}
