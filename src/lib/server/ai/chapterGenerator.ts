// src/lib/server/ai/index.ts

import { extractHook } from '$lib/server/ai/hookExtractor';
import { initializeOpenAIClient } from '$lib/server/ai/openAI';
import { retrieveRelevantContext } from '$lib/server/ai/vectorize';
import { createDbClient } from '$lib/server/db';
import type { Series } from '$lib/server/db/schema';
import { chapters } from '$lib/server/db/schema';
import { fillTemplate } from '$lib/utils/template';
import { desc, eq } from 'drizzle-orm';
import { zodResponseFormat } from 'openai/helpers/zod.mjs';
import type { ParsedChatCompletion } from 'openai/resources/chat/completions.mjs';
import { z } from 'zod';
import { genres, type GenreKey } from './genres';

const StorySchema = z.object({
	title: z.string().describe('The catchy, appealing title of the story in Vietnamese.'),
	content: z.string().describe('The full story text in Vietnamese, formatted using simple Markdown.')
});

type StoryOutput = z.infer<typeof StorySchema>;

type NextChapterOutput = StoryOutput & {
	userPrompt: string;
	modelName: string;
};

/**
 * Generates the next chapter for an existing series, acting as an "AI Director".
 * @param series - The full series object from the database, including its genre.
 * @param env - The Cloudflare platform environment.
 * @returns The complete data for the new chapter.
 */
export async function generateChapter(series: Series, env: App.Platform['env']): Promise<NextChapterOutput> {
	const db = createDbClient(env.DB);

	const openaiClient = initializeOpenAIClient({
		baseURL: env.PROXY_OPENAI_URL,
		secret: env.PROXY_API_KEY
	});

	const genrePack = genres[<GenreKey>series.genre];

	const lastChapter = await db.query.chapters.findFirst({
		where: eq(chapters.seriesId, series.id),
		orderBy: [desc(chapters.chapterNumber)]
	});

	if (!lastChapter) {
		throw new Error(`No chapters found for seriesId: ${series.id} to generate the next one.`);
	}

	const hook = await extractHook({
		openaiClient,
		chapterContent: lastChapter.content,
		genrePack
	});

	const context = await retrieveRelevantContext({
		openaiClient,
		vectorizeIndex: env.VECTORIZE,
		genrePack,
		query: hook,
		seriesId: series.id
	});

	const fullPrompt = fillTemplate(genrePack.nextChapterPrompt, {
		previousChaptersContext: context,
		chapterNumber: (lastChapter.chapterNumber + 1).toString()
	});

	const {
		success,
		message,
		data: response
	} = await openaiClient.completions<ParsedChatCompletion<StoryOutput>>({
		model: genrePack.mainModel,
		messages: [
			{ role: 'system', content: genrePack.nextChapterSystemPrompt },
			{ role: 'user', content: fullPrompt }
		],
		response_format: zodResponseFormat(StorySchema, 'output')
	});

	if (!success) {
		console.error('[AI Story Generator] Failed:', message);
		throw new Error('Failed to generate story.');
	}

	if (!response.choices[0].message.content) {
		throw new Error('AI did not call the required tool.');
	}

	const storyData = <StoryOutput>JSON.parse(response.choices[0].message.content);

	// 5. Assemble and return the final object.
	return {
		...storyData,
		userPrompt: fullPrompt,
		modelName: genrePack.mainModel
	};
}
