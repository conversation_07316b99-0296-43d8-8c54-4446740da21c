/**
 * A simple template filler. Replaces all occurrences of {{key}} with the corresponding value.
 * @param template The template string with {{placeholders}}.
 * @param data An object where keys match the placeholder names.
 * @returns The filled template string.
 */
export function fillTemplate(template: string, data: Record<string, string>): string {
	let result = template;
	for (const key in data) {
		const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
		result = result.replace(regex, data[key]);
	}
	return result;
}
