// src/hooks.server.ts
import { error, type Handle } from '@sveltejs/kit';

/**
 * Configuration for API key authentication
 */
const AUTH_CONFIG = {
	// Routes that should be excluded from API key authentication
	// Add patterns here if you want some routes to be public
	excludedRoutes: [
		// Example: '/health', '/public/*'
		'/(public)/*'
	] as string[],

	// Whether to require API key for all routes (set to false to use excludedRoutes)
	protectAllRoutes: true,

	// Log authentication attempts
	enableLogging: true
};

/**
 * Check if a route should be protected based on configuration
 */
function shouldProtectRoute(pathname: string): boolean {
	if (!AUTH_CONFIG.protectAllRoutes) {
		return !AUTH_CONFIG.excludedRoutes.some((pattern) => {
			if (pattern.endsWith('*')) {
				return pathname.startsWith(pattern.slice(0, -1));
			}
			return pathname === pattern;
		});
	}

	// If protecting all routes, check if this route is excluded
	return !AUTH_CONFIG.excludedRoutes.some((pattern) => {
		if (pattern.endsWith('*')) {
			return pathname.startsWith(pattern.slice(0, -1));
		}
		return pathname === pattern;
	});
}

/**
 * SvelteKit server hooks for API key authentication
 * Protects all requests with an API key stored in Cloudflare secrets
 */
export const handle: Handle = async ({ event, resolve }) => {
	// Check if this route should be protected
	if (!shouldProtectRoute(event.url.pathname)) {
		if (AUTH_CONFIG.enableLogging) {
			console.log('[Auth] Route excluded from protection:', event.url.pathname);
		}
		return resolve(event);
	}
	// Get the API secret from Cloudflare environment
	const apiSecret = event.platform?.env?.API_SECRET;

	if (!apiSecret) {
		console.error('[Auth] API_SECRET not found in environment');
		throw error(500, 'Server configuration error: API_SECRET not configured');
	}

	// Extract API key from request headers
	const providedApiKey =
		event.request.headers.get('x-api-key') ||
		event.request.headers.get('authorization')?.replace('Bearer ', '') ||
		event.url.searchParams.get('api_key');

	// Check if API key is provided
	if (!providedApiKey) {
		console.warn('[Auth] Request rejected: No API key provided', {
			url: event.url.pathname,
			method: event.request.method,
			userAgent: event.request.headers.get('user-agent')
		});

		throw error(
			401,
			'API key required. Provide API key via x-api-key header, Authorization Bearer token, or api_key query parameter'
		);
	}

	// Validate API key
	if (providedApiKey !== apiSecret) {
		console.warn('[Auth] Request rejected: Invalid API key', {
			url: event.url.pathname,
			method: event.request.method,
			providedKey: providedApiKey.substring(0, 8) + '...', // Log only first 8 chars for security
			userAgent: event.request.headers.get('user-agent')
		});

		throw error(403, 'Invalid API key. The provided API key is not valid');
	}

	// Log successful authentication
	console.log('[Auth] Request authenticated successfully', {
		url: event.url.pathname,
		method: event.request.method
	});

	// Continue with the request
	const response = await resolve(event);

	// Add security headers
	response.headers.set('X-Content-Type-Options', 'nosniff');
	response.headers.set('X-Frame-Options', 'DENY');
	response.headers.set('X-XSS-Protection', '1; mode=block');

	return response;
};
