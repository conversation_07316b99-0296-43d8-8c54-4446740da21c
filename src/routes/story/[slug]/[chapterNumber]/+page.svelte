<script lang="ts">
	import { goto } from '$app/navigation';
	import type { PageData } from './$types';
	const { data }: { data: PageData } = $props();

	let series = $derived(data.series);
	let incomingChapterNumber = $derived(data.incomingChapterNumber);
	let chapterPromise = $derived(data.chapterPromise);

	function formatContent(content: string) {
		const paragraphs = content
			.replace(/\\u001f/g, '')
			.replace(/\\"/g, '"')
			.split('\n\n')
			.map((p) => p.trim());

		return paragraphs;
	}

	// const paragraphs = content
	// 	.replace(/\\u001f/g, '')
	// 	.replace(/\\"/g, '"')
	// 	.split('\n\n')
	// 	.map((p) => p.trim());
</script>

<!-- <main class="bg-gray-50 py-10 font-sans">
	<div class="mx-auto max-w-4xl px-4">
		<header class="mb-10 border-b pb-6 text-center">
			<a href="/" class="mb-6 inline-block text-blue-600 hover:underline">&larr; Back to Home</a>
			<h1 class="mb-3 text-4xl font-extrabold tracking-tight text-gray-900">
				{series.title}
			</h1>
		</header>

		{#await chapterPromise}
			<div class="rounded-lg bg-white px-6 py-20 text-center shadow-sm">
				<h2 class="mb-4 text-2xl font-bold">
					Chương {incomingChapterNumber}
				</h2>
				<div class="animate-pulse text-lg text-gray-600">
					Hệ thống đang sáng tác, vui lòng chờ trong giây lát...
				</div>
			</div>
		{:then chapter}
			<article class="rounded-lg bg-white p-6 shadow-sm sm:p-8">
				<h2 class="mb-6 border-b pb-3 text-2xl font-bold text-gray-800 sm:text-3xl">
					Chương {chapter.chapterNumber}: {chapter.title}
				</h2>
				<div class="prose prose-lg max-w-none leading-relaxed text-gray-800">
					{@html chapter.content}
				</div>
			</article>

			{#if chapter.chapterNumber}
				<footer class="mt-8 flex items-center justify-between">
					{#if chapter.chapterNumber > 1}
						<a
							href="/story/{series.slug}/{chapter.chapterNumber - 1}"
							class="rounded-lg border bg-white px-4 py-2 hover:bg-gray-100"
						>
							&larr; Chương Trước
						</a>
					{:else}
						<span></span>
					{/if}
					<a
						href="/story/{series.slug}/{chapter.chapterNumber + 1}"
						class="rounded-lg border bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
					>
						Chương Tiếp Theo &rarr;
					</a>
				</footer>
			{/if}
		{:catch error}
			<div class="rounded-lg border border-red-200 bg-red-50 px-6 py-20 text-center">
				<p class="text-lg text-red-700">Rất tiếc, đã có lỗi xảy ra trong quá trình sáng tác.</p>
				<p class="mt-2 text-sm text-red-600">{error.message}</p>
			</div>
		{/await}
	</div>
</main> -->

<div class="flex min-h-screen flex-col items-center bg-gray-100">
	<main class="mt-6 w-full max-w-[700px] rounded-lg bg-white px-6 py-8 shadow-md">
		<header class="mb-10 border-b pb-6 text-center">
			<a href="/" class="mb-6 inline-block text-blue-600 hover:underline">&larr; Back to Home</a>
		</header>

		{#await chapterPromise}
			<div class="rounded-lg bg-white px-6 py-20 text-center shadow-sm">
				<h2 class="mb-4 text-2xl font-bold">
					Chương {incomingChapterNumber}
				</h2>
				<div class="animate-pulse text-lg text-gray-600">Hệ thống đang sáng tác, vui lòng chờ trong giây lát...</div>
			</div>
		{:then chapter}
			{@const chapNumber = chapter.chapterNumber}

			<h1 class="mb-2 text-center text-2xl font-bold">{chapter.title}</h1>
			<p class="mb-6 text-center text-sm text-gray-500">Chương {chapter.chapterNumber}</p>

			<div class="text-justify text-base leading-7 text-gray-800">
				{#each formatContent(chapter.content) as paragraph}
					<p class="mb-4">{paragraph}</p>
				{/each}
			</div>

			{#if chapNumber !== undefined}
				<div class="mt-8 flex justify-between">
					<button
						class="rounded bg-gray-200 px-4 py-2 hover:bg-gray-300 disabled:opacity-50"
						onclick={() => goto(`/series/${series.slug}/chapter/${chapNumber - 1}`)}
						disabled={chapNumber === 1}
					>
						Chương trước
					</button>

					<button
						class="rounded bg-gray-200 px-4 py-2 hover:bg-gray-300"
						onclick={() => goto(`/series/${series.slug}/chapter/${chapNumber + 1}`)}
					>
						Chương tiếp
					</button>
				</div>
			{/if}
		{:catch error}
			<div class="rounded-lg border border-red-200 bg-red-50 px-6 py-20 text-center">
				<p class="text-lg text-red-700">Rất tiếc, đã có lỗi xảy ra trong quá trình sáng tác.</p>
				<p class="mt-2 text-sm text-red-600">{error.message}</p>
			</div>
		{/await}
	</main>
</div>
