// src/routes/api/generate-story/+server.ts

import { genres, type GenreKey } from '$lib/server/ai/genres';
import { initializeOpenAIClient } from '$lib/server/ai/openAI';
import { generateNewSeries } from '$lib/server/ai/seriesGenerator';
import { ingestChapterIntoVectorize } from '$lib/server/ai/vectorize';
import { createDbClient } from '$lib/server/db';
import { chapterChunks, chapters, series } from '$lib/server/db/schema';
import { slugify } from '$lib/utils/slugify';
import { json } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { ulid } from 'ulid';
import type { RequestHandler } from './$types';
import { GoogleGenAI } from '@google/genai';

export const GET: RequestHandler = async ({ platform }) => {
	if (!platform?.env.DB || !platform?.env.PROXY_OPENAI_URL || !platform?.env.PROXY_API_KEY) {
		console.error('Server configuration error: Missing required environment bindings.');
		return json({ error: 'Server is not configured correctly.' }, { status: 500 });
	}

	try {
		const db = createDbClient(platform.env.DB);

		const openAIClient = initializeOpenAIClient({
			baseURL: platform.env.PROXY_OPENAI_URL,
			secret: platform.env.PROXY_API_KEY
		});

		const genAIClient = new GoogleGenAI({ apiKey: platform.env.GENAI_API_KEY });

		const genreToGenerate: GenreKey = 'rebirth';

		const output = await generateNewSeries(openAIClient, genAIClient, genreToGenerate);

		console.log('[Job Action] Inserting new series data...');

		const newSeries = (
			await db
				.insert(series)
				.values({
					id: ulid(),
					title: output.title,
					slug: `${slugify(output.title)}-${ulid().slice(-6)}`,
					description: output.content.slice(0, 250) + '...',
					genre: genreToGenerate,

					theme: output.theme,
					idea: output.idea,
					summary: output.summary,
					outline: output.outline,

					createdAt: new Date().toISOString()
				})
				.returning()
		)[0];
		console.log('[Job Action] Inserted new series data:', newSeries);

		console.log('[Job Action] Inserting new chapter data...');
		const newChapter = (
			await db
				.insert(chapters)
				.values({
					id: ulid(),
					chapterNumber: 1,
					title: output.title,
					content: output.content,

					userPrompt: output.userPrompt,
					modelName: genres[genreToGenerate].mainModel,
					usageMetadata: JSON.stringify(output.usageMetadata),

					semanticQueries: JSON.stringify(output.semantic_queries),
					metadata: JSON.stringify(output.metadata),

					createdAt: new Date().toISOString(),

					seriesId: newSeries.id
				})
				.returning()
		)[0];
		console.log('[Job Action] Inserted new chapter data:', newChapter);

		platform.context.waitUntil(
			ingestChapterIntoVectorize({
				openAIClient,
				vectorizeIndex: platform.env.VECTORIZE,
				genrePack: genres[genreToGenerate],
				chapter: newChapter
			})
				.then(async (vectors) => {
					console.log('[Job Action] Successfully ingested chapter into Vectorize.');

					await db.insert(chapterChunks).values(
						vectors.map((vector) => {
							return {
								id: vector.id,
								chapterId: newChapter.id,
								seriesId: newSeries.id,
								chunkIndex: vector.metadata.chunkIndex,
								sentenceStart: vector.metadata.sentenceStart,
								sentenceEnd: vector.metadata.sentenceEnd,
								text: vector.metadata.text,
								metadata: JSON.stringify(vector.metadata),
								createdAt: new Date().toISOString()
							};
						})
					);

					await db.update(chapters).set({ isVectorized: true }).where(eq(chapters.id, newChapter.id));
				})
				.catch((error) => {
					console.error('[Job Error] Failed to ingest chapter into Vectorize:', error);
				})
		);

		return json({ success: true, action: 'created_new_series', newSeries, newChapter });
	} catch (error) {
		console.error('[Job Error] Failed to process story generation job:', error);
		const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
		return json({ error: 'Failed to process request.', details: errorMessage }, { status: 500 });
	}
};

// Ensure this endpoint is always dynamic
export const prerender = false;
