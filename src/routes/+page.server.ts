// src/routes/+page.server.ts
import { createDbClient } from '$lib/server/db';
import type { PageServerLoad } from './$types';
import { series } from '$lib/server/db/schema';
import { desc } from 'drizzle-orm';

export const load: PageServerLoad = async ({ platform }) => {
	if (!platform?.env.DB) {
		console.error('D1 Database binding not found.');
		// Return empty array on error so the page can still render
		return { allSeries: [] };
	}

	try {
		const db = createDbClient(platform.env.DB);

		// Use Drizzle to fetch all series, ordered by the newest first
		const allSeries = await db.query.series.findMany({
			orderBy: [desc(series.createdAt)]
		});

		return { allSeries };
	} catch (e) {
		console.error('Failed to fetch series:', e);
		return { allSeries: [] };
	}
};
