// src/routes/story/[slug]/[chapterNumber=integer]/+page.server.ts
import { createDbClient } from '$lib/server/db';
import { error } from '@sveltejs/kit';
import { and, eq } from 'drizzle-orm';
import { series as seriesSchema, chapters as chaptersSchema, chapters } from '$lib/server/db/schema';
import { generateChapter } from '$lib/server/ai/chapterGenerator.js';
import { ingestChapterIntoVectorize } from '$lib/server/ai/vectorize';
import { initializeOpenAIClient } from '$lib/server/ai/openAI.js';
import { genres, type GenreKey } from '$lib/server/ai/genres';
import { ulid } from 'ulid';

export const load = async ({ platform, params }) => {
	if (!platform?.env.DB) throw error(500, 'Database not available');

	const db = createDbClient(platform.env.DB);
	const seriesSlug = params.slug;
	const chapterNumber = parseInt(params.chapterNumber, 10);

	const seriesData = await db.query.series.findFirst({
		where: eq(seriesSchema.slug, seriesSlug)
	});

	if (!seriesData) throw error(404, 'Story series not found');

	const totalChapters = (
		await db.select({ id: chaptersSchema.id }).from(chaptersSchema).where(eq(chaptersSchema.seriesId, seriesData.id))
	).length;

	if (chapterNumber <= totalChapters) {
		console.log('[Page Load] Fetching existing chapter from database.');
		const chapterData = await db.query.chapters.findFirst({
			where: and(eq(chaptersSchema.seriesId, seriesData.id), eq(chaptersSchema.chapterNumber, chapterNumber))
		});

		console.log('[Page Load] Existing series data:', seriesData);

		return {
			series: seriesData,
			chapterPromise: Promise.resolve({
				...chapterData,
				content: chapterData!.content
			})
		};
	} else if (chapterNumber === totalChapters + 1) {
		const chapterGenerationPromise = async () => {
			const output = await generateChapter(seriesData, platform.env);

			const newChapter = (
				await db
					.insert(chapters)
					.values({
						id: ulid(),
						title: output.title,
						content: output.content,
						chapterNumber: chapterNumber,
						seriesId: seriesData.id,
						createdAt: new Date().toISOString()
					})
					.returning()
			)[0];

			// Kích hoạt ingestion nền
			platform.context.waitUntil(
				ingestChapterIntoVectorize({
					openAIClient: initializeOpenAIClient({
						baseURL: platform.env.PROXY_OPENAI_URL,
						secret: platform.env.PROXY_API_KEY
					}),
					vectorizeIndex: platform.env.VECTORIZE,
					genrePack: genres[<GenreKey>seriesData.genre],
					chapter: newChapter
				})
			);

			return {
				...newChapter,
				content: newChapter.content
			};
		};

		return {
			series: seriesData,
			incomingChapterNumber: chapterNumber,
			chapterPromise: chapterGenerationPromise()
		};
	} else {
		throw error(404, 'Chapter not found');
	}
};
