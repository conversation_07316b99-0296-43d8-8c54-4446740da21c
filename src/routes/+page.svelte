<script lang="ts">
	import type { PageData } from './$types';

	const { data }: { data: PageData } = $props();

	let allSeries = $derived(data.allSeries);
</script>

<main class="mx-auto max-w-4xl px-4 py-8 sm:px-6 lg:py-16">
	<header class="mb-12 text-center">
		<h1 class="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl">Kho Tàng Truyện AI</h1>
		<p class="mt-4 text-lg text-gray-600">Những cuộc phiêu lưu bất tận được tạo ra bởi Trí <PERSON> T<PERSON></p>
	</header>

	<div class="space-y-8">
		{#if allSeries && allSeries.length > 0}
			{#each allSeries as storySeries}
				<article
					class="block rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition hover:border-gray-300 hover:shadow-lg"
				>
					<h2 class="text-2xl font-bold text-gray-800">
						<a href="/story/{storySeries.slug}/1" class="hover:text-blue-600">
							{storySeries.title}
						</a>
					</h2>

					{#if storySeries.description}
						<p class="mt-2 line-clamp-3 text-gray-600">
							{storySeries.description}
						</p>
					{/if}

					<div class="mt-4 text-sm text-gray-500">
						<span>Tạo lúc: {new Date(storySeries.createdAt).toLocaleDateString('vi-VN')}</span>
					</div>
				</article>
			{/each}
		{:else}
			<div class="rounded-lg bg-gray-50 px-6 py-16 text-center">
				<p class="text-lg text-gray-500">Kho truyện đang được sáng tác...</p>
				<p class="mt-2 text-sm text-gray-400">(Cron Job đang làm việc, vui lòng quay lại sau!)</p>
			</div>
		{/if}
	</div>
</main>
