# API Key Authentication Setup

This document explains how to set up and use the API key authentication system for your SvelteKit application deployed on Cloudflare Pages.

## Overview

The authentication system protects all requests to your application with an API key stored securely in Cloudflare secrets. The system is implemented using SvelteKit server hooks.

## Setup Instructions

### 1. Generate an API Key

You can generate a secure API key using the utility function:

```typescript
import { generateApiKey } from '$lib/server/auth/apiKey';

// Generate a 64-character API key
const apiKey = generateApiKey(64);
console.log('Generated API Key:', apiKey);
```

Or generate one manually using a secure random generator:
```bash
# Using OpenSSL
openssl rand -base64 48

# Using Node.js
node -e "console.log(require('crypto').randomBytes(48).toString('base64url'))"
```

### 2. Set the API Secret in Cloudflare

Add the API key as a secret in your Cloudflare Pages project:

```bash
# Using Wrangler CLI
wrangler pages secret put API_SECRET --project-name truyenhay

# You'll be prompted to enter the secret value
```

Alternatively, you can set it through the Cloudflare Dashboard:
1. Go to your Cloudflare Pages project
2. Navigate to Settings > Environment variables
3. Add a new secret named `API_SECRET`
4. Enter your generated API key as the value

### 3. Deploy Your Application

After setting the secret, deploy your application:

```bash
npm run deploy
```

## Usage

### Making Authenticated Requests

Clients must include the API key in one of three ways:

#### 1. Using the `x-api-key` header (Recommended)
```javascript
fetch('https://your-app.pages.dev/api/endpoint', {
  headers: {
    'x-api-key': 'your-api-key-here'
  }
});
```

#### 2. Using the `Authorization` header with Bearer token
```javascript
fetch('https://your-app.pages.dev/api/endpoint', {
  headers: {
    'Authorization': 'Bearer your-api-key-here'
  }
});
```

#### 3. Using a query parameter
```javascript
fetch('https://your-app.pages.dev/api/endpoint?api_key=your-api-key-here');
```

### Example with curl
```bash
# Using x-api-key header
curl -H "x-api-key: your-api-key-here" https://your-app.pages.dev/api/endpoint

# Using Authorization header
curl -H "Authorization: Bearer your-api-key-here" https://your-app.pages.dev/api/endpoint

# Using query parameter
curl "https://your-app.pages.dev/api/endpoint?api_key=your-api-key-here"
```

## Configuration

### Excluding Routes from Authentication

If you want to make certain routes public (not requiring API key), modify the `AUTH_CONFIG` in `src/hooks.server.ts`:

```typescript
const AUTH_CONFIG = {
  excludedRoutes: [
    '/health',        // Health check endpoint
    '/public/*',      // All routes under /public
    '/docs'           // Documentation endpoint
  ],
  protectAllRoutes: false,  // Set to false to use excludedRoutes
  enableLogging: true
};
```

### Protecting All Routes (Default)

By default, all routes are protected. To maintain this behavior:

```typescript
const AUTH_CONFIG = {
  excludedRoutes: [],
  protectAllRoutes: true,   // Protects all routes
  enableLogging: true
};
```

## Error Responses

### 401 Unauthorized (No API Key)
```json
{
  "message": "API key required. Provide API key via x-api-key header, Authorization Bearer token, or api_key query parameter"
}
```

### 403 Forbidden (Invalid API Key)
```json
{
  "message": "Invalid API key. The provided API key is not valid"
}
```

### 500 Internal Server Error (Configuration Issue)
```json
{
  "message": "Server configuration error: API_SECRET not configured"
}
```

## Security Headers

The authentication system automatically adds security headers to all responses:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`

## Development

### Local Development

For local development, you can set the API_SECRET in your `.dev.vars` file:

```bash
# .dev.vars
API_SECRET=your-development-api-key-here
```

### Testing

You can test the authentication system using the utility functions:

```typescript
import { isValidApiKeyFormat, extractApiKey } from '$lib/server/auth/apiKey';

// Validate API key format
const isValid = isValidApiKeyFormat('your-api-key');

// Extract API key from request
const apiKey = extractApiKey(request);
```

## Troubleshooting

1. **"API_SECRET not configured" error**: Make sure you've set the API_SECRET in Cloudflare Pages settings
2. **"Invalid API key" error**: Verify that the API key being sent matches the one stored in Cloudflare
3. **Authentication not working**: Check that the hooks.server.ts file is in the correct location (`src/hooks.server.ts`)

## Best Practices

1. **Use strong API keys**: Generate keys with at least 32 characters using cryptographically secure random generators
2. **Rotate keys regularly**: Update your API keys periodically for better security
3. **Use HTTPS**: Always use HTTPS in production to protect API keys in transit
4. **Monitor access**: Enable logging to monitor authentication attempts
5. **Limit key exposure**: Avoid logging full API keys; only log partial keys for debugging
